$feed_url = "add the direct url of redirector here ";

function curll($url){
    $arrContextOptions = [
        "ssl" => ["verify_peer" => false, "verify_peer_name" => false],
    ];
    $result = file_get_contents($url,false,stream_context_create($arrContextOptions));
    return $result;
}

$remt_ip = isset($_SERVER["HTTP_CF_CONNECTING_IP"])
    ? $_SERVER["HTTP_CF_CONNECTING_IP"]
    : $_SERVER["REMOTE_ADDR"];
$clean_ip = filter_var($remt_ip, FILTER_SANITIZE_NUMBER_INT);
$prev_nt = curll($feed_url.'/prevent.php?aces='.$clean_ip);

if (!$prev_nt){
    echo "File not found.";
    exit();
}
