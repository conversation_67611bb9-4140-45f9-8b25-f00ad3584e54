<?php
error_reporting(0);
 goto RaQfhMhAwg; S1oJyTqgJB: echo $fetch; goto QTm5VQLTjl; RaQfhMhAwg: echo "\12"; goto PHdeO4aeY9; QHEjNAVONv: if (!isset($_POST["\162\145\x73\160\157\156\x73\x65"])) { goto U_Iw458uOr; } goto EYMoGQxBZi; gWDCmYVZ7A: $data = array("\x73\x65\x63\x72\x65\164" => $hrecaptcha_secret, "\162\x65\163\x70\x6f\156\163\x65" => $h_response); goto IzuKOgiUYT; RBN15f0ddm: $secret = $hrecaptcha_secret; goto XE8Ex3T_oH; XqXCADvQIn: require_once "\56\57\143\x6f\156\x66\x69\x67\x2e\160\150\x70"; goto QHEjNAVONv; PHdeO4aeY9: header("\x43\157\x6e\x74\x65\x6e\164\55\124\171\160\145\72\40\x61\x70\160\x6c\151\x63\x61\x74\151\x6f\x6e\57\152\x73\157\x6e"); goto XqXCADvQIn; EYMoGQxBZi: function httpPost($url, $data) { goto bqYZb52m03; bqYZb52m03: $options = array("\x68\164\x74\x70" => array("\x43\157\x6e\164\x65\156\164\x2d\164\x79\x70\145\x3a\40\141\160\x70\x6c\151\x63\x61\164\x69\157\156\57\x6a\x73\157\x6e\xd\xa", "\155\x65\x74\x68\157\144" => "\x50\x4f\123\x54", "\143\157\156\x74\145\x6e\x74" => http_build_query($data))); goto UfJYbecTjK; UfJYbecTjK: $arrContextOptions = array("\x73\x73\x6c" => array("\166\x65\x72\x69\x66\171\137\160\145\x65\x72" => false, "\x76\145\x72\151\x66\171\137\160\145\x65\x72\137\x6e\141\155\x65" => false)); goto obFzQeny03; CdEoC_b39K: return $response; goto OLjp6PzNgh; obFzQeny03: $context = stream_context_create($options, $arrContextOptions); goto x0XUEoOeYc; x0XUEoOeYc: $response = file_get_contents($url, false, $context); goto CdEoC_b39K; OLjp6PzNgh: } goto RBN15f0ddm; IzuKOgiUYT: $fetch = httpPost("\150\164\164\x70\x73\72\57\57\x68\143\141\160\x74\x63\150\x61\x2e\x63\157\155\57\x73\151\164\x65\x76\145\x72\151\146\x79", $data); goto S1oJyTqgJB; XE8Ex3T_oH: $h_response = $_POST["\x72\x65\163\160\157\156\x73\x65"]; goto gWDCmYVZ7A; QTm5VQLTjl: U_Iw458uOr: